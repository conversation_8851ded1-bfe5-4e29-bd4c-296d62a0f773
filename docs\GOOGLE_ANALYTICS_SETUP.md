# Google Analytics 4 Integration Guide

## Overview

This guide explains how to set up and use Google Analytics 4 (GA4) in the NEUQUIP React application.

## Phase 1: Basic Setup (Completed)

### Files Added/Modified

1. **Package Dependencies**
   - Added `react-ga4` to `package.json`

2. **Analytics Utilities**
   - `src/utils/analytics.ts` - Core analytics functions
   - `src/hooks/useAnalytics.ts` - React hook for analytics

3. **Configuration**
   - `src/services/config.ts` - GA4 configuration
   - `.env.development` - Development environment variables
   - `.env.production` - Production environment variables
   - `.env.example` - Example environment file

4. **Initialization**
   - `src/main.tsx` - GA4 initialization
   - `index.html` - Basic gtag setup

## Setup Instructions

### 1. Create Google Analytics 4 Property

1. Go to [Google Analytics](https://analytics.google.com/)
2. Create a new GA4 property for your website
3. Get your Measurement ID (format: G-XXXXXXXXXX)

### 2. Configure Environment Variables

Create or update your environment files:

**For Development (.env.development):**
```env
VITE_GA4_MEASUREMENT_ID_DEV=G-YOUR-DEV-ID
```

**For Production (.env.production):**
```env
VITE_GA4_MEASUREMENT_ID_PROD=G-YOUR-PROD-ID
```

### 3. Install Dependencies

```bash
npm install react-ga4
```

## Usage

### Basic Page Tracking

Page views are automatically tracked when using the `useAnalytics` hook:

```typescript
import { useAnalytics } from '../hooks/useAnalytics';

const MyComponent = () => {
  const analytics = useAnalytics(); // Automatically tracks page views
  
  return <div>My Component</div>;
};
```

### Event Tracking

```typescript
import { useAnalytics } from '../hooks/useAnalytics';

const MyComponent = () => {
  const { trackEvent, trackNavigation, trackChatMessage } = useAnalytics();
  
  const handleButtonClick = () => {
    trackEvent({
      category: 'User Interaction',
      action: 'Button Click',
      label: 'Header CTA',
    });
  };
  
  const handleNavigation = () => {
    trackNavigation('/dashboard', 'header-menu');
  };
  
  const handleChatMessage = () => {
    trackChatMessage('user', true); // user message with file
  };
  
  return (
    <div>
      <button onClick={handleButtonClick}>Click Me</button>
      <button onClick={handleNavigation}>Go to Dashboard</button>
      <button onClick={handleChatMessage}>Send Message</button>
    </div>
  );
};
```

### Available Tracking Methods

- `trackNavigation(destination, source)` - Track navigation events
- `trackSidebarToggle(isCollapsed)` - Track sidebar interactions
- `trackChatMessage(type, hasFile)` - Track chat interactions
- `trackFileUpload(fileType, fileSize)` - Track file uploads
- `trackSketchbookAction(action, type)` - Track sketchbook actions
- `trackPaymentEvent(action, amount, currency)` - Track payment events
- `trackWorkflowAction(action, type)` - Track workflow actions
- `trackPerformance(metric, value, unit)` - Track performance metrics
- `trackError(errorType, message, code)` - Track errors
- `trackUserLogin(method)` - Track user login
- `trackUserLogout()` - Track user logout
- `trackUserRegistration(method)` - Track user registration

## Configuration Options

The analytics system supports various configuration options:

```typescript
export const GA4_CONFIG = {
  measurementId: 'G-XXXXXXXXXX',
  debug: true, // Enable debug mode in development
  testMode: false, // Enable test mode
  anonymizeIp: true, // Anonymize IP addresses for privacy
  cookieFlags: 'SameSite=None;Secure', // Cookie configuration
};
```

## Privacy Considerations

- IP addresses are anonymized by default
- Cookie consent will be implemented in Phase 4
- Debug mode is only enabled in development
- Test mode can be enabled for testing without affecting production data

## Debugging

### Development Mode
- Set `debug: true` in configuration
- Check browser console for analytics logs
- Use Google Analytics Debugger extension

### Verification
1. Check Google Analytics Real-time reports
2. Use GA4 Debug View
3. Verify events in GA4 Events report

## Next Phases

### Phase 2: Page View Tracking
- Automatic route change tracking
- Custom page titles
- Route-specific tracking

### Phase 3: Custom Event Tracking
- Component-specific events
- User interaction tracking
- Business metric tracking

### Phase 4: Privacy & Compliance
- Cookie consent banner
- GDPR compliance
- Opt-out functionality

### Phase 5: Environment Configuration
- Production deployment
- Testing setup
- Performance optimization

## Troubleshooting

### Common Issues

1. **Analytics not working**
   - Check if Measurement ID is set correctly
   - Verify environment variables are loaded
   - Check browser console for errors

2. **Events not showing in GA4**
   - Events may take 24-48 hours to appear in standard reports
   - Use Real-time reports for immediate verification
   - Check if debug mode is enabled

3. **Development vs Production**
   - Ensure different Measurement IDs for dev/prod
   - Check environment variable configuration
   - Verify build process includes correct environment

## Support

For issues or questions about the analytics implementation, refer to:
- [Google Analytics 4 Documentation](https://developers.google.com/analytics/devguides/collection/ga4)
- [react-ga4 Documentation](https://github.com/codler/react-ga4)
- Project documentation in `/docs/`
