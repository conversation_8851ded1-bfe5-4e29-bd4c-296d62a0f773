import ReactDOM from 'react-dom/client';
import { Provider } from 'react-redux';
import App from './App';
import './index.css';
import './styles/darkModeTransitions.css';
import './styles/tourStyles.css';
import { store } from './store/store';
import { Toaster } from 'react-hot-toast';

// Initialize Google Analytics
import { initializeGA4 } from './utils/analytics';
import { GA4_CONFIG } from './services/config';

// Import and register Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  RadialLinearScale,
  ArcElement,
} from 'chart.js';

// Register Chart.js components globally
ChartJS.register(
  CategoryScale,
  LinearScale,
  TimeScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  RadialLinearScale,
  Filler
);

// Initialize Google Analytics 4
if (GA4_CONFIG.measurementId) {
  initializeGA4(GA4_CONFIG).catch((error) => {
    console.error('Failed to initialize GA4:', error);
  });
} else {
  console.warn('GA4 Measurement ID not found. Analytics will be disabled.');
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <Provider store={store}>
    <Toaster />
    <App />
  </Provider>
);
