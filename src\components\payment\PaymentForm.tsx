import React, { useState, useEffect } from 'react';
import { CardElement } from '@stripe/react-stripe-js';
import {
  Box,
  Typography,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import { useStripePayment } from '../../hooks/payment/useStripe';
import { usePayment } from '../../hooks/payment/usePayment';
import { PaymentFormData } from '../../types/payment';
import { formatCurrency } from '../../utils/payment/stripeHelpers';
import { validateEmail } from '../../utils/payment/paymentValidation';
import {
  getCurrentUserId,
  getCurrentUserEmail,
} from '../../utils/auth/userHelpers';
import { useTheme as useThemeContext } from '../../contexts/ThemeContext';
import styles from './PaymentForm.module.css';
import CustomInput from '../common/input/CustomInput';
import PaymentDiagnostics from './PaymentDiagnostics';
import { FaLock, FaShieldAlt } from 'react-icons/fa';
import { BugReport } from '@mui/icons-material';

const PaymentForm: React.FC = () => {
  const {
    isReady,
    isProcessing,
    cardComplete,
    cardError,
    handleCardChange,
    processPayment,
    resetPayment,
  } = useStripePayment();

  const {
    currentPlan,
    paymentError,
    clearPaymentError,
    handlePaymentSuccess,
    handlePaymentError,
  } = usePayment();

  const themeContext = useThemeContext();

  // Get current user info for pre-filling form
  const currentUserId = getCurrentUserId();
  const currentUserEmail = getCurrentUserEmail();

  const [formData, setFormData] = useState<PaymentFormData>({
    planId: currentPlan?.id || '',
    customerEmail: currentUserEmail || '',
    customerName: '',
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  useEffect(() => {
    if (currentPlan) {
      setFormData((prev) => ({
        ...prev,
        planId: currentPlan.id,
        customerEmail: prev.customerEmail || currentUserEmail || '',
      }));
    }
  }, [currentPlan, currentUserEmail]);

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.customerEmail) {
      errors.customerEmail = 'Email is required';
    } else if (!validateEmail(formData.customerEmail)) {
      errors.customerEmail = 'Please enter a valid email address';
    }

    if (!formData.customerName || formData.customerName.trim().length < 2) {
      errors.customerName = 'Please enter your full name';
    }

    if (!cardComplete) {
      errors.card = 'Please complete your card information';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange =
    (field: keyof PaymentFormData) =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: event.target.value,
      }));

      // Clear field error when user starts typing
      if (formErrors[field]) {
        setFormErrors((prev) => ({
          ...prev,
          [field]: '',
        }));
      }
    };

  const handleRetry = async () => {
    if (retryCount >= 3) {
      handlePaymentError(
        'Maximum retry attempts reached. Please try again later or contact support.'
      );
      return;
    }

    setIsRetrying(true);
    setRetryCount((prev) => prev + 1);
    clearPaymentError();

    try {
      const paymentRecord = await processPayment(formData);
      handlePaymentSuccess(paymentRecord);
      setRetryCount(0); // Reset retry count on success
    } catch (error: any) {
      const errorMessage = error.message || 'Payment failed';
      handlePaymentError(errorMessage);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!isReady || isProcessing || !currentPlan) {
      return;
    }

    // Check if user is authenticated
    if (!currentUserId) {
      handlePaymentError(
        'User not authenticated. Please log in and try again.'
      );
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    clearPaymentError();

    try {
      const paymentRecord = await processPayment(formData);
      handlePaymentSuccess(paymentRecord);
      setRetryCount(0); // Reset retry count on success
    } catch (error: any) {
      const errorMessage = error.message || 'Payment failed';
      handlePaymentError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    resetPayment();
    setFormData({
      planId: currentPlan?.id || '',
      customerEmail: '',
      customerName: '',
    });
    setFormErrors({});
    setIsSubmitting(false);
  };

  if (!currentPlan) {
    return (
      <Box className={styles.container}>
        <Alert severity="error">
          No payment plan selected. Please go back and select a plan.
        </Alert>
      </Box>
    );
  }

  return (
    <div className={styles.container}>
      {/* Plan Summary Card */}
      <div className={styles.planCard}>
        <div className={styles.planHeader}>
          <Typography variant="h6" className={styles.planName}>
            {currentPlan.name}
          </Typography>
          <Typography variant="h4" className={styles.planPrice}>
            {formatCurrency(currentPlan.price, currentPlan.currency)}
            {currentPlan.interval && (
              <span className={styles.planInterval}>
                /{currentPlan.interval}
              </span>
            )}
          </Typography>
        </div>
        <div className={styles.planDetails}>
          <Typography variant="body2" className={styles.planDescription}>
            {currentPlan.description}
          </Typography>
          <Typography variant="body2" className={styles.planFeatures}>
            {currentPlan.tokens} tokens • {currentPlan.maxGraphs} graphs
          </Typography>
        </div>
      </div>

      {/* Payment Form */}
      <form onSubmit={handleSubmit} className={styles.paymentForm}>
        {/* Error Display */}
        {paymentError && (
          <div className={styles.errorContainer}>
            <Alert severity="error" className={styles.errorAlert}>
              {paymentError}
            </Alert>
            <div className={styles.errorActions}>
              {(paymentError.includes('timeout') ||
                paymentError.includes('unavailable')) &&
                retryCount < 3 && (
                  <Button
                    variant="outlined"
                    onClick={handleRetry}
                    disabled={isRetrying || isSubmitting || isProcessing}
                    className={styles.retryButton}
                    size="small"
                  >
                    {isRetrying ? (
                      <>
                        <CircularProgress
                          size={16}
                          className={styles.retrySpinner}
                        />
                        Retrying...
                      </>
                    ) : (
                      `Retry (${3 - retryCount} attempts left)`
                    )}
                  </Button>
                )}
              <Button
                variant="text"
                onClick={() => setShowDiagnostics(true)}
                className={styles.diagnosticsButton}
                size="small"
                startIcon={<BugReport />}
              >
                Run Diagnostics
              </Button>
            </div>
          </div>
        )}

        {/* Customer Information */}
        <div className={styles.formSection}>
          <CustomInput
            label="Email"
            type="email"
            value={formData.customerEmail}
            onChange={handleInputChange('customerEmail')}
            error={!!formErrors.customerEmail}
            helperText={formErrors.customerEmail}
            required
            fullWidth
            className={styles.inputField}
          />

          <CustomInput
            label="Full name"
            type="text"
            value={formData.customerName}
            onChange={handleInputChange('customerName')}
            error={!!formErrors.customerName}
            helperText={formErrors.customerName}
            required
            fullWidth
            className={styles.inputField}
          />
        </div>

        {/* Card Information */}
        <div className={styles.formSection}>
          <label className={styles.cardLabel}>
            Card information
            <span className={styles.required}>*</span>
          </label>
          <div
            className={`${styles.cardContainer} ${cardError || formErrors.card ? styles.cardError : ''} ${cardComplete ? styles.cardComplete : ''}`}
          >
            <CardElement
              onChange={handleCardChange}
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    fontFamily:
                      '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                    color: themeContext.isDarkMode ? '#ffffff' : '#32325d',
                    backgroundColor: 'transparent',
                    iconColor: themeContext.isDarkMode ? '#ffffff' : '#6772e5',
                    '::placeholder': {
                      color: themeContext.isDarkMode ? '#87bbfd' : '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#fa755a',
                    iconColor: '#fa755a',
                  },
                  complete: {
                    color: themeContext.isDarkMode ? '#ffffff' : '#32325d',
                  },
                },
                hidePostalCode: false,
              }}
              className={styles.cardElement}
            />
          </div>
          {(cardError || formErrors.card) && (
            <div className={styles.fieldError}>
              {cardError || formErrors.card}
            </div>
          )}
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="contained"
          disabled={!isReady || isSubmitting || isProcessing || !cardComplete}
          className={styles.payButton}
          fullWidth
        >
          {isSubmitting || isProcessing ? (
            <>
              <CircularProgress size={20} className={styles.buttonSpinner} />
              Processing...
            </>
          ) : (
            <>
              <FaLock className={styles.lockIcon} />
              Pay {formatCurrency(currentPlan.price, currentPlan.currency)}
            </>
          )}
        </Button>

        {/* Security Footer */}
        <div className={styles.securityFooter}>
          <div className={styles.securityItem}>
            <FaShieldAlt className={styles.securityIcon} />
            <span>Secured by Stripe</span>
          </div>
          <div className={styles.securityDivider}>•</div>
          <div className={styles.securityItem}>
            <FaLock className={styles.securityIcon} />
            <span>SSL encrypted</span>
          </div>
        </div>
      </form>

      {/* Payment Diagnostics Dialog */}
      <PaymentDiagnostics
        open={showDiagnostics}
        onClose={() => setShowDiagnostics(false)}
        planId={currentPlan?.id}
        userId={currentUserId || undefined}
      />
    </div>
  );
};

export default PaymentForm;
