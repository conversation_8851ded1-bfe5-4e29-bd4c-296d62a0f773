import { useCallback, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import {
  trackPageView,
  trackEvent,
  setUserProperties,
  trackLogin,
  trackLogout,
  trackRegistration,
  isAnalyticsEnabled,
  AnalyticsEvent,
  UserProperties,
  EventCategories,
  EventActions,
} from '../utils/analytics';
import useLocalStorage from './useLocalStorage';

/**
 * Custom hook for Google Analytics tracking
 */
export const useAnalytics = () => {
  const location = useLocation();
  const [user] = useLocalStorage('user', null);
  const previousPath = useRef<string>('');

  // Track page views automatically on route changes
  useEffect(() => {
    const currentPath = location.pathname + location.search;
    
    // Avoid tracking the same page twice
    if (previousPath.current !== currentPath && isAnalyticsEnabled()) {
      // Generate page title based on route
      const pageTitle = getPageTitle(location.pathname);
      
      trackPageView(currentPath, pageTitle);
      previousPath.current = currentPath;
    }
  }, [location]);

  // Set user properties when user data changes
  useEffect(() => {
    if (user && isAnalyticsEnabled()) {
      const userProperties: UserProperties = {
        user_id: user.id || user._id,
        user_role: user.role,
        subscription_status: user.subscriptionStatus || 'free',
      };

      setUserProperties(userProperties);
    }
  }, [user]);

  // Manual page tracking
  const trackPage = useCallback((path: string, title?: string) => {
    if (isAnalyticsEnabled()) {
      trackPageView(path, title);
    }
  }, []);

  // Event tracking with predefined categories
  const trackAnalyticsEvent = useCallback((event: AnalyticsEvent) => {
    if (isAnalyticsEnabled()) {
      trackEvent(event);
    }
  }, []);

  // Navigation events
  const trackNavigation = useCallback((destination: string, source?: string) => {
    trackAnalyticsEvent({
      category: EventCategories.NAVIGATION,
      action: EventActions.MENU_CLICK,
      label: destination,
      custom_parameters: {
        source: source || 'unknown',
      },
    });
  }, [trackAnalyticsEvent]);

  // Sidebar events
  const trackSidebarToggle = useCallback((isCollapsed: boolean) => {
    trackAnalyticsEvent({
      category: EventCategories.NAVIGATION,
      action: EventActions.SIDEBAR_TOGGLE,
      label: isCollapsed ? 'collapsed' : 'expanded',
    });
  }, [trackAnalyticsEvent]);

  // Content interaction events
  const trackChatMessage = useCallback((messageType: 'user' | 'ai', hasFile?: boolean) => {
    trackAnalyticsEvent({
      category: EventCategories.CONTENT,
      action: EventActions.CHAT_MESSAGE,
      label: messageType,
      custom_parameters: {
        has_file: hasFile || false,
      },
    });
  }, [trackAnalyticsEvent]);

  const trackFileUpload = useCallback((fileType: string, fileSize?: number) => {
    trackAnalyticsEvent({
      category: EventCategories.CONTENT,
      action: EventActions.FILE_UPLOAD,
      label: fileType,
      value: fileSize,
    });
  }, [trackAnalyticsEvent]);

  const trackSketchbookAction = useCallback((action: 'create' | 'edit' | 'delete', sketchbookType?: string) => {
    trackAnalyticsEvent({
      category: EventCategories.CONTENT,
      action: action === 'create' ? EventActions.SKETCHBOOK_CREATE : EventActions.SKETCHBOOK_EDIT,
      label: sketchbookType || 'unknown',
    });
  }, [trackAnalyticsEvent]);

  // E-commerce events
  const trackPaymentEvent = useCallback((action: 'initiate' | 'success' | 'cancel', amount?: number, currency?: string) => {
    const eventAction = action === 'initiate' ? EventActions.PAYMENT_INITIATE :
                       action === 'success' ? EventActions.PAYMENT_SUCCESS :
                       EventActions.PAYMENT_CANCEL;

    trackAnalyticsEvent({
      category: EventCategories.ECOMMERCE,
      action: eventAction,
      value: amount,
      custom_parameters: {
        currency: currency || 'USD',
      },
    });
  }, [trackAnalyticsEvent]);

  // Workflow events
  const trackWorkflowAction = useCallback((action: 'create' | 'approve' | 'reject', workflowType?: string) => {
    const eventAction = action === 'create' ? EventActions.WORKFLOW_CREATE :
                       action === 'approve' ? EventActions.WORKFLOW_APPROVE :
                       EventActions.WORKFLOW_REJECT;

    trackAnalyticsEvent({
      category: EventCategories.WORKFLOW,
      action: eventAction,
      label: workflowType || 'unknown',
    });
  }, [trackAnalyticsEvent]);

  // Performance tracking
  const trackPerformance = useCallback((metric: string, value: number, unit?: string) => {
    trackAnalyticsEvent({
      category: EventCategories.PERFORMANCE,
      action: EventActions.PAGE_LOAD_TIME,
      label: metric,
      value: Math.round(value),
      custom_parameters: {
        unit: unit || 'ms',
      },
    });
  }, [trackAnalyticsEvent]);

  // Error tracking
  const trackError = useCallback((errorType: string, errorMessage?: string, errorCode?: string) => {
    trackAnalyticsEvent({
      category: EventCategories.ERROR,
      action: EventActions.ERROR_OCCURRED,
      label: errorType,
      custom_parameters: {
        error_message: errorMessage,
        error_code: errorCode,
      },
    });
  }, [trackAnalyticsEvent]);

  // Authentication events
  const trackUserLogin = useCallback((method: string = 'email') => {
    trackLogin(method);
  }, []);

  const trackUserLogout = useCallback(() => {
    trackLogout();
  }, []);

  const trackUserRegistration = useCallback((method: string = 'email') => {
    trackRegistration(method);
  }, []);

  return {
    // Manual tracking
    trackPage,
    trackEvent: trackAnalyticsEvent,
    
    // Specific event tracking
    trackNavigation,
    trackSidebarToggle,
    trackChatMessage,
    trackFileUpload,
    trackSketchbookAction,
    trackPaymentEvent,
    trackWorkflowAction,
    trackPerformance,
    trackError,
    
    // Authentication tracking
    trackUserLogin,
    trackUserLogout,
    trackUserRegistration,
  };
};

/**
 * Generate page title based on route path
 */
const getPageTitle = (pathname: string): string => {
  const routeTitles: Record<string, string> = {
    '/': 'Home',
    '/chat': 'Chat',
    '/my-projects': 'My Projects',
    '/library': 'Library',
    '/awaiting-actions': 'Awaiting Actions',
    '/awaited-actions-view': 'Awaited Actions View',
    '/sketchbooklists': 'Sketchbooks',
    '/sketchbook': 'Sketchbook Editor',
    '/workflow': 'Workflow',
    '/home-final': 'Home Final',
    '/edit-profile': 'Edit Profile',
    '/dashboard': 'Admin Dashboard',
    '/login': 'Login',
    '/signup': 'Sign Up',
    '/price': 'Pricing',
    '/payment': 'Payment',
    '/payment/success': 'Payment Success',
    '/payment/error': 'Payment Error',
    '/forgot-password': 'Forgot Password',
    '/reset-password': 'Reset Password',
  };

  // Handle dynamic routes
  if (pathname.startsWith('/sketchbook/')) {
    return 'Sketchbook Editor';
  }

  return routeTitles[pathname] || 'NEUQUIP';
};

export default useAnalytics;
