import ReactGA from 'react-ga4';

// Analytics configuration interface
export interface AnalyticsConfig {
  measurementId: string;
  debug?: boolean;
  testMode?: boolean;
  cookieFlags?: string;
  anonymizeIp?: boolean;
}

// Event tracking interface
export interface AnalyticsEvent {
  action: string;
  category: string;
  label?: string;
  value?: number;
  custom_parameters?: Record<string, any>;
}

// User properties interface
export interface UserProperties {
  user_id?: string;
  user_role?: string;
  subscription_status?: string;
  theme_preference?: string;
  device_type?: string;
}

// Analytics state
let isInitialized = false;
let isEnabled = true;
let currentConfig: AnalyticsConfig | null = null;

/**
 * Load GA4 script dynamically
 */
const loadGA4Script = (measurementId: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if script is already loaded
    if (document.querySelector(`script[src*="gtag/js?id=${measurementId}"]`)) {
      resolve();
      return;
    }

    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`;
    script.onload = () => resolve();
    script.onerror = () => reject(new Error('Failed to load GA4 script'));
    document.head.appendChild(script);
  });
};

/**
 * Initialize Google Analytics 4
 */
export const initializeGA4 = async (config: AnalyticsConfig): Promise<void> => {
  try {
    if (isInitialized) {
      console.warn('GA4 already initialized');
      return;
    }

    if (!config.measurementId) {
      console.error('GA4 Measurement ID is required');
      return;
    }

    // Store configuration
    currentConfig = config;

    // Load GA4 script dynamically
    await loadGA4Script(config.measurementId);

    // Initialize ReactGA
    ReactGA.initialize(config.measurementId, {
      testMode: config.testMode || false,
      gaOptions: {
        debug_mode: config.debug || false,
        anonymize_ip: config.anonymizeIp !== false, // Default to true
        cookie_flags: config.cookieFlags || 'SameSite=None;Secure',
      },
    });

    isInitialized = true;

    if (config.debug) {
      console.log('✅ GA4 initialized successfully', {
        measurementId: config.measurementId,
        debug: config.debug,
        testMode: config.testMode,
      });
    }
  } catch (error) {
    console.error('❌ Failed to initialize GA4:', error);
  }
};

/**
 * Track page view
 */
export const trackPageView = (path: string, title?: string): void => {
  if (!isInitialized || !isEnabled) return;

  try {
    ReactGA.send({
      hitType: 'pageview',
      page: path,
      title: title || document.title,
    });

    if (currentConfig?.debug) {
      console.log('📊 Page view tracked:', { path, title });
    }
  } catch (error) {
    console.error('❌ Failed to track page view:', error);
  }
};

/**
 * Track custom event
 */
export const trackEvent = (event: AnalyticsEvent): void => {
  if (!isInitialized || !isEnabled) return;

  try {
    ReactGA.event({
      category: event.category,
      action: event.action,
      label: event.label,
      value: event.value,
      ...event.custom_parameters,
    });

    if (currentConfig?.debug) {
      console.log('📊 Event tracked:', event);
    }
  } catch (error) {
    console.error('❌ Failed to track event:', error);
  }
};

/**
 * Set user properties
 */
export const setUserProperties = (properties: UserProperties): void => {
  if (!isInitialized || !isEnabled) return;

  try {
    ReactGA.set(properties);

    if (currentConfig?.debug) {
      console.log('👤 User properties set:', properties);
    }
  } catch (error) {
    console.error('❌ Failed to set user properties:', error);
  }
};

/**
 * Track user login
 */
export const trackLogin = (method: string = 'email'): void => {
  trackEvent({
    category: 'User Authentication',
    action: 'Login',
    label: method,
  });
};

/**
 * Track user logout
 */
export const trackLogout = (): void => {
  trackEvent({
    category: 'User Authentication',
    action: 'Logout',
  });
};

/**
 * Track user registration
 */
export const trackRegistration = (method: string = 'email'): void => {
  trackEvent({
    category: 'User Authentication',
    action: 'Registration',
    label: method,
  });
};

/**
 * Enable analytics tracking
 */
export const enableAnalytics = (): void => {
  isEnabled = true;
  if (currentConfig?.debug) {
    console.log('✅ Analytics enabled');
  }
};

/**
 * Disable analytics tracking
 */
export const disableAnalytics = (): void => {
  isEnabled = false;
  if (currentConfig?.debug) {
    console.log('🚫 Analytics disabled');
  }
};

/**
 * Check if analytics is initialized
 */
export const isAnalyticsInitialized = (): boolean => {
  return isInitialized;
};

/**
 * Check if analytics is enabled
 */
export const isAnalyticsEnabled = (): boolean => {
  return isEnabled && isInitialized;
};

/**
 * Get current configuration
 */
export const getAnalyticsConfig = (): AnalyticsConfig | null => {
  return currentConfig;
};

// Predefined event categories for consistency
export const EventCategories = {
  USER_AUTH: 'User Authentication',
  NAVIGATION: 'Navigation',
  CONTENT: 'Content Interaction',
  ECOMMERCE: 'E-commerce',
  WORKFLOW: 'Workflow',
  PERFORMANCE: 'Performance',
  ERROR: 'Error',
} as const;

// Predefined event actions for consistency
export const EventActions = {
  // Authentication
  LOGIN: 'Login',
  LOGOUT: 'Logout',
  REGISTER: 'Registration',

  // Navigation
  PAGE_VIEW: 'Page View',
  SIDEBAR_TOGGLE: 'Sidebar Toggle',
  MENU_CLICK: 'Menu Click',

  // Content
  CHAT_MESSAGE: 'Chat Message',
  FILE_UPLOAD: 'File Upload',
  SKETCHBOOK_CREATE: 'Sketchbook Create',
  SKETCHBOOK_EDIT: 'Sketchbook Edit',

  // E-commerce
  PAYMENT_INITIATE: 'Payment Initiate',
  PAYMENT_SUCCESS: 'Payment Success',
  PAYMENT_CANCEL: 'Payment Cancel',

  // Workflow
  WORKFLOW_CREATE: 'Workflow Create',
  WORKFLOW_APPROVE: 'Workflow Approve',
  WORKFLOW_REJECT: 'Workflow Reject',

  // Performance
  PAGE_LOAD_TIME: 'Page Load Time',
  API_RESPONSE_TIME: 'API Response Time',

  // Error
  ERROR_OCCURRED: 'Error Occurred',
  API_ERROR: 'API Error',
} as const;
