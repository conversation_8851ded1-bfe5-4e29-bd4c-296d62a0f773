const DEV = false; // means production

//false; //means production
//true; //means development

const PRO_BASE_URL = import.meta.env.VITE_PRO_BASE_URL;
const DEV_BASE_URL = import.meta.env.VITE_DEV_BASE_URL;

const PRO_AI_BASE_URL = import.meta.env.VITE_PRO_AI_BASE_URL;
const DEV_AI_BASE_URL = import.meta.env.VITE_DEV_AI_BASE_URL;

// Google Analytics Configuration
const GA4_MEASUREMENT_ID_DEV = import.meta.env.VITE_GA4_MEASUREMENT_ID_DEV;
const GA4_MEASUREMENT_ID_PROD = import.meta.env.VITE_GA4_MEASUREMENT_ID_PROD;

// Stripe Configuration
const STRIPE_PUBLISHABLE_KEY_TEST = import.meta.env
  .VITE_STRIPE_PUBLISHABLE_KEY_TEST;
// const STRIPE_PUBLISHABLE_KEY_LIVE = import.meta.env
//   .VITE_STRIPE_PUBLISHABLE_KEY_LIVE;

export const BASE_URL = DEV ? DEV_BASE_URL : PRO_BASE_URL;
export const AI_BASE_URL = DEV ? DEV_AI_BASE_URL : PRO_AI_BASE_URL;

// Google Analytics Configuration
export const GA4_MEASUREMENT_ID = DEV
  ? GA4_MEASUREMENT_ID_DEV
  : GA4_MEASUREMENT_ID_PROD;
export const GA4_CONFIG = {
  measurementId: GA4_MEASUREMENT_ID,
  debug: DEV,
  testMode: DEV,
  anonymizeIp: true,
  cookieFlags: 'SameSite=None;Secure',
};

// export const STRIPE_PUBLISHABLE_KEY = DEV
//   ? STRIPE_PUBLISHABLE_KEY_TEST
//   : STRIPE_PUBLISHABLE_KEY_LIVE;

export const STRIPE_PUBLISHABLE_KEY = STRIPE_PUBLISHABLE_KEY_TEST;
